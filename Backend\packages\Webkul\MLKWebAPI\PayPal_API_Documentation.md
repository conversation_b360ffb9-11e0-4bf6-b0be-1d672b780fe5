# PayPal API 接口文档

本文档描述了在 MLKWebAPI 模块中实现的 PayPal 支付处理和回调的 API 接口。

## 概述

MLKWebAPI 模块现在支持两种 PayPal 支付方式：
1. **PayPal Smart Button** - 现代化的 JavaScript SDK 集成
2. **PayPal Standard** - 传统的重定向式支付

所有 API 接口都遵循 MLKWebAPI 的标准响应格式，并使用 Laravel Sanctum 进行身份验证。

## 认证

除了 IPN 回调接口外，所有 PayPal API 接口都需要 Sanctum 令牌认证：

```http
Authorization: Bearer {your-sanctum-token}
```

## API 接口

### 1. PayPal Smart Button - 创建订单

**端点：** `POST /api/mlk/paypal/smart-button/create-order`

**描述：** 为 PayPal Smart Button 创建支付订单

**认证：** 必需

**请求体：** 无

**响应示例：**
```json
{
    "success": true,
    "message": "PayPal order created successfully",
    "data": {
        "paypal_order": {
            "id": "5O190127TN364715T",
            "status": "CREATED",
            "links": [...]
        },
        "cart": {...}
    },
    "locale": "en"
}
```

### 2. PayPal Smart Button - 捕获支付

**端点：** `POST /api/mlk/paypal/smart-button/capture-order`

**描述：** 捕获 PayPal Smart Button 支付并创建订单

**认证：** 必需

**请求体：**
```json
{
    "orderData": {
        "orderID": "5O190127TN364715T"
    }
}
```

**响应示例：**
```json
{
    "success": true,
    "message": "PayPal payment completed successfully",
    "data": {
        "order": {...},
        "paypal_order_id": "5O190127TN364715T",
        "redirect_url": "https://example.com/checkout/success"
    },
    "locale": "en"
}
```

### 3. PayPal Standard - 获取重定向信息

**端点：** `GET /api/mlk/paypal/standard/redirect-info`

**描述：** 获取 PayPal Standard 支付的重定向 URL 和表单字段

**认证：** 必需

**响应示例：**
```json
{
    "success": true,
    "message": "PayPal redirect information retrieved successfully",
    "data": {
        "paypal_url": "https://www.sandbox.paypal.com/cgi-bin/webscr",
        "form_fields": {
            "cmd": "_xclick",
            "business": "<EMAIL>",
            "item_name": "Order #123",
            "amount": "100.00",
            "currency_code": "USD",
            "return": "https://example.com/api/mlk/paypal/standard/success",
            "cancel_return": "https://example.com/api/mlk/paypal/standard/cancel"
        },
        "cart": {...}
    },
    "locale": "en"
}
```

### 4. PayPal Standard - 支付成功回调

**端点：** `GET /api/mlk/paypal/standard/success`

**描述：** 处理 PayPal Standard 支付成功回调

**认证：** 必需

**查询参数：** PayPal 返回的参数

**响应示例：**
```json
{
    "success": true,
    "message": "PayPal payment completed successfully",
    "data": {
        "order": {...},
        "redirect_url": "https://example.com/checkout/success"
    },
    "locale": "en"
}
```

### 5. PayPal Standard - 支付取消回调

**端点：** `GET /api/mlk/paypal/standard/cancel`

**描述：** 处理 PayPal Standard 支付取消回调

**认证：** 必需

**响应示例：**
```json
{
    "success": false,
    "message": "PayPal payment has been cancelled",
    "data": {
        "redirect_url": "https://example.com/cart"
    },
    "locale": "en"
}
```

### 6. 获取支付状态

**端点：** `GET /api/mlk/paypal/payment-status`

**描述：** 获取指定订单的支付状态

**认证：** 必需

**查询参数：**
- `order_id` (必需): 订单ID

**响应示例：**
```json
{
    "success": true,
    "message": "Payment status retrieved successfully",
    "data": {
        "order": {...},
        "payment_status": "processing",
        "payment_method": "paypal_smart_button",
        "total_amount": 100.00,
        "currency": "USD"
    },
    "locale": "en"
}
```

### 7. PayPal IPN 处理

**端点：** `POST /api/mlk/paypal/ipn`

**描述：** 处理 PayPal 即时付款通知 (IPN)

**认证：** 不需要（PayPal 服务器回调）

**请求体：** PayPal IPN 数据

**响应示例：**
```json
{
    "status": "success"
}
```

## 错误处理

所有 API 接口都使用统一的错误响应格式：

```json
{
    "success": false,
    "message": "错误描述",
    "data": {
        "error_details": "详细错误信息"
    },
    "locale": "en"
}
```

常见的 HTTP 状态码：
- `200` - 成功
- `400` - 客户端错误（如购物车为空）
- `401` - 未认证
- `422` - 验证失败
- `500` - 服务器错误

## 使用流程

### PayPal Smart Button 流程

1. 前端调用 `POST /api/mlk/paypal/smart-button/create-order` 创建 PayPal 订单
2. 使用返回的订单ID初始化 PayPal Smart Button
3. 用户完成支付后，前端调用 `POST /api/mlk/paypal/smart-button/capture-order` 捕获支付
4. 系统创建订单并返回成功信息

### PayPal Standard 流程

1. 前端调用 `GET /api/mlk/paypal/standard/redirect-info` 获取重定向信息
2. 使用返回的 URL 和表单字段重定向用户到 PayPal
3. 用户完成支付后，PayPal 重定向到成功或取消回调 URL
4. 系统处理回调并创建订单

## 配置要求

确保在 Bagisto 配置中正确设置了 PayPal 相关配置：

1. PayPal 商户账户信息
2. PayPal API 凭据
3. 沙盒/生产环境设置
4. 回调 URL 配置

## 测试

可以使用提供的测试文件进行功能测试：

```bash
php artisan test packages/Webkul/MLKWebAPI/src/Tests/Feature/PayPalControllerTest.php
```

## 注意事项

1. 所有金额都使用购物车的货币代码
2. 订单创建前会进行完整的购物车验证
3. 支付成功后会自动创建发票（如果订单支持）
4. 所有操作都会记录详细的日志用于调试
5. IPN 处理是异步的，不依赖用户会话
