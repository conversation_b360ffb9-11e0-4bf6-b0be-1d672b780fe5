<?php

namespace Webkul\MLKWebAPI\Http\Controllers\API;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Webkul\Checkout\Facades\Cart;
use Webkul\Payment\Facades\Payment;
use Webkul\Sales\Repositories\OrderRepository;
use Webkul\Sales\Repositories\InvoiceRepository;
use Webkul\Sales\Http\Resources\OrderResource;
use Webkul\Paypal\Payment\SmartButton;
use Webkul\Paypal\Payment\Standard;
use Webkul\Paypal\Helpers\Ipn;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class PayPalController extends APIController
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(
        protected OrderRepository $orderRepository,
        protected InvoiceRepository $invoiceRepository,
        protected SmartButton $smartButton,
        protected Standard $standard,
        protected Ipn $ipnHelper
    ) {}

    /**
     * PayPal Smart Button - 创建订单
     * 
     * @return JsonResponse
     */
    public function createSmartButtonOrder(): JsonResponse
    {
        try {
            // 获取当前认证用户
            $customer = auth()->guard('sanctum')->user();
            
            // 确保Cart实例使用当前用户
            Cart::initCart($customer);
            
            $cart = Cart::getCart();
            if (!$cart) {
                return $this->error(trans('mlk::app.checkout.cart.not-found'));
            }

            if (Cart::hasError()) {
                return $this->error(implode(': ', Cart::getErrors()) ?: 'Cart has errors');
            }

            // 验证购物车是否选择了PayPal Smart Button支付方式
            if (!$cart->payment || $cart->payment->method !== 'paypal_smart_button') {
                return $this->error(trans('mlk::app.payment.paypal.payment-method-not-selected'));
            }

            Cart::collectTotals();

            // 构建PayPal订单请求体
            $requestBody = $this->buildSmartButtonRequestBody($cart);
            
            // 调用PayPal API创建订单
            $response = $this->smartButton->createOrder($requestBody);
            
            return $this->success([
                'paypal_order' => $response->result,
                'cart' => $cart,
            ], trans('mlk::app.payment.paypal.order-created-successfully'));

        } catch (\Exception $e) {
            Log::error('PayPal Smart Button Order Creation Failed: ' . $e->getMessage(), [
                'customer_id' => auth()->guard('sanctum')->id(),
                'cart_id' => Cart::getCart()?->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->error(
                trans('mlk::app.payment.paypal.order-creation-failed'),
                500,
                ['error_details' => $e->getMessage()]
            );
        }
    }

    /**
     * PayPal Smart Button - 捕获支付
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function captureSmartButtonOrder(Request $request): JsonResponse
    {
        try {
            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'orderData' => 'required|array',
                'orderData.orderID' => 'required|string',
            ]);

            if ($validator->fails()) {
                return $this->validationFailed(
                    trans('mlk::app.payment.paypal.invalid-order-data'),
                    $validator->errors()->toArray()
                );
            }

            // 获取当前认证用户
            $customer = auth()->guard('sanctum')->user();
            
            // 确保Cart实例使用当前用户
            Cart::initCart($customer);
            
            $cart = Cart::getCart();
            if (!$cart) {
                return $this->error(trans('mlk::app.checkout.cart.not-found'));
            }

            if (Cart::hasError()) {
                return $this->error(implode(': ', Cart::getErrors()) ?: 'Cart has errors');
            }

            Cart::collectTotals();

            // 验证订单
            try {
                $this->validateOrder();
            } catch (\Exception $e) {
                return $this->error($e->getMessage());
            }

            // 捕获PayPal支付
            $paypalOrderId = $request->input('orderData.orderID');
            $this->smartButton->captureOrder($paypalOrderId);

            // 创建系统订单
            $data = (new OrderResource($cart))->jsonSerialize();
            $order = $this->orderRepository->create($data);

            // 更新订单状态为处理中
            $this->orderRepository->update(['status' => 'processing'], $order->id);

            // 如果可以开发票，自动创建发票
            if ($order->canInvoice()) {
                $invoiceData = $this->prepareInvoiceData($order);
                $this->invoiceRepository->create($invoiceData);
            }

            // 停用购物车
            Cart::deActivateCart();

            Log::info('PayPal Smart Button Payment Captured Successfully', [
                'customer_id' => $customer->id,
                'order_id' => $order->id,
                'paypal_order_id' => $paypalOrderId,
                'amount' => $order->grand_total
            ]);

            return $this->success([
                'order' => $order,
                'paypal_order_id' => $paypalOrderId,
                'redirect_url' => route('shop.checkout.onepage.success'),
            ], trans('mlk::app.payment.paypal.payment-completed-successfully'));

        } catch (\Exception $e) {
            Log::error('PayPal Smart Button Payment Capture Failed: ' . $e->getMessage(), [
                'customer_id' => auth()->guard('sanctum')->id(),
                'paypal_order_id' => $request->input('orderData.orderID'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->error(
                trans('mlk::app.payment.paypal.payment-capture-failed'),
                500,
                ['error_details' => $e->getMessage()]
            );
        }
    }

    /**
     * PayPal Standard - 获取重定向信息
     * 
     * @return JsonResponse
     */
    public function getStandardRedirectInfo(): JsonResponse
    {
        try {
            // 获取当前认证用户
            $customer = auth()->guard('sanctum')->user();
            
            // 确保Cart实例使用当前用户
            Cart::initCart($customer);
            
            $cart = Cart::getCart();
            if (!$cart) {
                return $this->error(trans('mlk::app.checkout.cart.not-found'));
            }

            if (Cart::hasError()) {
                return $this->error(implode(': ', Cart::getErrors()) ?: 'Cart has errors');
            }

            // 验证购物车是否选择了PayPal Standard支付方式
            if (!$cart->payment || $cart->payment->method !== 'paypal_standard') {
                return $this->error(trans('mlk::app.payment.paypal.payment-method-not-selected'));
            }

            Cart::collectTotals();

            // 获取PayPal表单字段
            $formFields = $this->standard->getFormFields();
            $paypalUrl = $this->standard->getPaypalUrl();

            return $this->success([
                'paypal_url' => $paypalUrl,
                'form_fields' => $formFields,
                'cart' => $cart,
            ], trans('mlk::app.payment.paypal.redirect-info-retrieved'));

        } catch (\Exception $e) {
            Log::error('PayPal Standard Redirect Info Failed: ' . $e->getMessage(), [
                'customer_id' => auth()->guard('sanctum')->id(),
                'cart_id' => Cart::getCart()?->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->error(
                trans('mlk::app.payment.paypal.redirect-info-failed'),
                500,
                ['error_details' => $e->getMessage()]
            );
        }
    }

    /**
     * PayPal Standard - 支付成功回调
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function standardSuccess(Request $request): JsonResponse
    {
        try {
            // 获取当前认证用户
            $customer = auth()->guard('sanctum')->user();
            
            // 确保Cart实例使用当前用户
            Cart::initCart($customer);
            
            $cart = Cart::getCart();
            if (!$cart) {
                return $this->error(trans('mlk::app.checkout.cart.not-found'));
            }

            // 创建订单
            $data = (new OrderResource($cart))->jsonSerialize();
            $order = $this->orderRepository->create($data);

            // 停用购物车
            Cart::deActivateCart();

            Log::info('PayPal Standard Payment Success', [
                'customer_id' => $customer->id,
                'order_id' => $order->id,
                'amount' => $order->grand_total,
                'paypal_params' => $request->all()
            ]);

            return $this->success([
                'order' => $order,
                'redirect_url' => route('shop.checkout.onepage.success'),
            ], trans('mlk::app.payment.paypal.payment-completed-successfully'));

        } catch (\Exception $e) {
            Log::error('PayPal Standard Success Callback Failed: ' . $e->getMessage(), [
                'customer_id' => auth()->guard('sanctum')->id(),
                'error' => $e->getMessage(),
                'paypal_params' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->error(
                trans('mlk::app.payment.paypal.success-callback-failed'),
                500,
                ['error_details' => $e->getMessage()]
            );
        }
    }

    /**
     * PayPal Standard - 支付取消回调
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function standardCancel(Request $request): JsonResponse
    {
        try {
            Log::info('PayPal Standard Payment Cancelled', [
                'customer_id' => auth()->guard('sanctum')->id(),
                'paypal_params' => $request->all()
            ]);

            return $this->error(
                trans('mlk::app.payment.paypal.payment-cancelled'),
                400,
                ['redirect_url' => route('shop.checkout.cart.index')]
            );

        } catch (\Exception $e) {
            Log::error('PayPal Standard Cancel Callback Failed: ' . $e->getMessage(), [
                'customer_id' => auth()->guard('sanctum')->id(),
                'error' => $e->getMessage(),
                'paypal_params' => $request->all()
            ]);

            return $this->error(
                trans('mlk::app.payment.paypal.cancel-callback-failed'),
                500,
                ['error_details' => $e->getMessage()]
            );
        }
    }

    /**
     * PayPal IPN (Instant Payment Notification) 处理
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function handleIPN(Request $request): JsonResponse
    {
        try {
            Log::info('PayPal IPN Received', [
                'ipn_data' => $request->all(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            // 处理IPN
            $this->ipnHelper->processIpn($request->all());

            return response()->json(['status' => 'success'], 200);

        } catch (\Exception $e) {
            Log::error('PayPal IPN Processing Failed: ' . $e->getMessage(), [
                'ipn_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json(['status' => 'error'], 500);
        }
    }

    /**
     * 获取PayPal支付状态
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getPaymentStatus(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'order_id' => 'required|integer|exists:orders,id',
            ]);

            if ($validator->fails()) {
                return $this->validationFailed(
                    trans('mlk::app.payment.paypal.invalid-order-id'),
                    $validator->errors()->toArray()
                );
            }

            $orderId = $request->input('order_id');
            $order = $this->orderRepository->findOrFail($orderId);

            // 验证订单归属权
            $customer = auth()->guard('sanctum')->user();
            if ($order->customer_id !== $customer->id) {
                return $this->unauthorized(trans('mlk::app.payment.paypal.unauthorized-order-access'));
            }

            return $this->success([
                'order' => $order,
                'payment_status' => $order->status,
                'payment_method' => $order->payment?->method,
                'total_amount' => $order->grand_total,
                'currency' => $order->order_currency_code,
            ], trans('mlk::app.payment.paypal.payment-status-retrieved'));

        } catch (\Exception $e) {
            Log::error('PayPal Payment Status Check Failed: ' . $e->getMessage(), [
                'customer_id' => auth()->guard('sanctum')->id(),
                'order_id' => $request->input('order_id'),
                'error' => $e->getMessage()
            ]);

            return $this->error(
                trans('mlk::app.payment.paypal.payment-status-failed'),
                500,
                ['error_details' => $e->getMessage()]
            );
        }
    }

    /**
     * 构建PayPal Smart Button请求体
     *
     * @param mixed $cart
     * @return array
     */
    private function buildSmartButtonRequestBody($cart): array
    {
        $items = [];
        foreach ($cart->items as $item) {
            $items[] = [
                'name' => $item->name,
                'unit_amount' => [
                    'currency_code' => $cart->cart_currency_code,
                    'value' => number_format($item->price, 2, '.', ''),
                ],
                'quantity' => $item->quantity,
                'sku' => $item->sku,
            ];
        }

        // 添加运费作为单独项目
        if ($cart->selected_shipping_rate && $cart->selected_shipping_rate->price > 0) {
            $items[] = [
                'name' => 'Shipping - ' . $cart->selected_shipping_rate->carrier_title,
                'unit_amount' => [
                    'currency_code' => $cart->cart_currency_code,
                    'value' => number_format($cart->selected_shipping_rate->price, 2, '.', ''),
                ],
                'quantity' => 1,
            ];
        }

        // 添加税费作为单独项目
        if ($cart->tax_total > 0) {
            $items[] = [
                'name' => 'Tax',
                'unit_amount' => [
                    'currency_code' => $cart->cart_currency_code,
                    'value' => number_format($cart->tax_total, 2, '.', ''),
                ],
                'quantity' => 1,
            ];
        }

        // 处理折扣
        if ($cart->discount_amount > 0) {
            $items[] = [
                'name' => 'Discount',
                'unit_amount' => [
                    'currency_code' => $cart->cart_currency_code,
                    'value' => '-' . number_format($cart->discount_amount, 2, '.', ''),
                ],
                'quantity' => 1,
            ];
        }

        return [
            'intent' => 'CAPTURE',
            'purchase_units' => [
                [
                    'reference_id' => $cart->id,
                    'amount' => [
                        'currency_code' => $cart->cart_currency_code,
                        'value' => number_format($cart->grand_total, 2, '.', ''),
                        'breakdown' => [
                            'item_total' => [
                                'currency_code' => $cart->cart_currency_code,
                                'value' => number_format(array_sum(array_map(function($item) {
                                    return floatval($item['unit_amount']['value']) * $item['quantity'];
                                }, $items)), 2, '.', ''),
                            ],
                        ],
                    ],
                    'items' => $items,
                    'shipping' => $this->buildShippingInfo($cart),
                ],
            ],
            'application_context' => [
                'brand_name' => core()->getCurrentChannel()->name,
                'locale' => app()->getLocale(),
                'landing_page' => 'BILLING',
                'shipping_preference' => 'SET_PROVIDED_ADDRESS',
                'user_action' => 'PAY_NOW',
                'return_url' => route('mlk.api.paypal.standard.success'),
                'cancel_url' => route('mlk.api.paypal.standard.cancel'),
            ],
        ];
    }

    /**
     * 构建运费信息
     *
     * @param mixed $cart
     * @return array
     */
    private function buildShippingInfo($cart): array
    {
        $shippingAddress = $cart->shipping_address;

        if (!$shippingAddress) {
            return [];
        }

        return [
            'name' => [
                'full_name' => $shippingAddress->name,
            ],
            'address' => [
                'address_line_1' => $shippingAddress->address1,
                'address_line_2' => $shippingAddress->address2,
                'admin_area_2' => $shippingAddress->city,
                'admin_area_1' => $shippingAddress->state,
                'postal_code' => $shippingAddress->postcode,
                'country_code' => $shippingAddress->country,
            ],
        ];
    }

    /**
     * 准备发票数据
     *
     * @param mixed $order
     * @return array
     */
    private function prepareInvoiceData($order): array
    {
        $invoiceData = [
            'order_id' => $order->id,
            'items' => [],
        ];

        foreach ($order->items as $item) {
            $invoiceData['items'][$item->id] = $item->qty_ordered;
        }

        return $invoiceData;
    }

    /**
     * 验证订单
     *
     * @return void
     * @throws \Exception
     */
    private function validateOrder(): void
    {
        $cart = Cart::getCart();

        if (!$cart) {
            throw new \Exception(trans('mlk::app.checkout.cart.not-found'));
        }

        if (!$cart->items->count()) {
            throw new \Exception(trans('mlk::app.checkout.cart.empty'));
        }

        if (!$cart->shipping_address) {
            throw new \Exception(trans('mlk::app.checkout.address.shipping-address-required'));
        }

        if (!$cart->billing_address) {
            throw new \Exception(trans('mlk::app.checkout.address.billing-address-required'));
        }

        if (!$cart->payment) {
            throw new \Exception(trans('mlk::app.checkout.payment.payment-method-required'));
        }

        if ($cart->haveStockableItems() && !$cart->shipping_method) {
            throw new \Exception(trans('mlk::app.checkout.shipping.shipping-method-required'));
        }
    }
}
