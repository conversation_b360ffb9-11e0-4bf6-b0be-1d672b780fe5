<?php

use Illuminate\Support\Facades\Route;
use Webkul\MLKWebAPI\Http\Controllers\API\CoreController;
use Webkul\MLKWebAPI\Http\Controllers\API\ProductController;
use Webkul\MLKWebAPI\Http\Controllers\API\CustomerController;
use Webkul\MLKWebAPI\Http\Controllers\API\CustomerDashboardController;
use Webkul\MLKWebAPI\Http\Controllers\API\IndexController;
use Webkul\MLKWebAPI\Http\Controllers\API\SearchController;
use Webkul\MLKWebAPI\Http\Controllers\API\SubscriptionController;
use Webkul\MLKWebAPI\Http\Controllers\API\PromotionController;
use Webkul\MLKWebAPI\Http\Controllers\API\NewsController;
use Webkul\MLKWebAPI\Http\Controllers\API\CartController;
use Webkul\MLKWebAPI\Http\Controllers\API\CheckoutController;
use Webkul\MLKWebAPI\Http\Controllers\API\WishlistController;
use Webkul\MLKWebAPI\Http\Controllers\API\CategoryProductController;
use Webkul\MLKWebAPI\Http\Controllers\API\AddressController;
use Webkul\MLKWebAPI\Http\Controllers\API\CustomerCouponController;
use Webkul\MLKWebAPI\Http\Controllers\API\OrderController;
use Webkul\MLKWebAPI\Http\Controllers\API\InvoiceController;

Route::group(['prefix' => 'api/mlk', 'middleware' => ['api_locale', 'api_auth']], function () {

    //首页
    Route::get('index',[IndexController::class,'index'])->name('mlk.api.home.index');
    
    //获取支付图标列表
    Route::get('common/payment-icons',[IndexController::class,'paymentIcons'])->name('mlk.api.home.payment_icons');
    /**
     * 核心路由
     */
    Route::controller(CoreController::class)->prefix('common')->group(function () {
        // 获取国家列表
        Route::get('countries', 'getCountries')->name('mlk.api.core.countries');
        //欧洲国家IP地址检测
        Route::get('check-ip', 'checkIp')->name('mlk.api.core.check_ip');
        // 获取州列表
        Route::get('states', 'getStates')->name('mlk.api.core.states');
        
        // 获取语言列表
        Route::get('locales', 'getLocales')->name('mlk.api.core.locales');

        // 获取分类列表
        Route::get('categories', 'getCategories')->name('mlk.api.core.categories');

        // 获取品牌列表
        Route::post('brands', 'getBrands')->name('mlk.api.core.brands');

        // 获取导航栏
        Route::get('navbar', 'getNavbar')->name('mlk.api.core.navbar');
        
        // 获取页头优惠信息
        Route::get('header-offer', 'getHeaderOffer')->name('mlk.api.core.header_offer');

        //获取底部页面内容
        Route::get('footer', 'getFooter')->name('mlk.api.core.footer');
    });

    /**
     * 搜索相关路由
     */
    Route::controller(SearchController::class)->prefix('search')->group(function () {
        // 商品搜索
        Route::get('', 'search')->name('mlk.api.search.index');
        
        // 图片搜索
        Route::post('upload-image', 'uploadImage')->name('mlk.api.search.upload_image');
    });
    
    
    /**
     * 产品相关路由
     */
    Route::controller(ProductController::class)->prefix('product')->group(function () {

        Route::get('detail', 'detail')->name('mlk.api.products.detail');

        Route::get('{id}/related', 'relatedProducts')->name('mlk.api.products.related.index');

        Route::get('{id}/up-sell', 'upSellProducts')->name('mlk.api.products.up-sell.index');
        
        Route::get('{id}/cross-sell', 'crossSellProducts')->name('mlk.api.products.cross-sell.index');
        
        // 记录产品访问统计
        Route::post('record-visit', 'recordVisit')->name('mlk.api.products.record-visit');
    });

    /**
     * 订阅相关路由
     */
    Route::controller(SubscriptionController::class)->prefix('subscription')->group(function () {
        // 订阅邮件通知 - 添加限流中间件，每分钟最多3次请求
        Route::middleware('throttle:5,1')->group(function () {
            Route::post('subscribe', 'subscribe')->name('mlk.api.subscription.subscribe');
            Route::middleware('auth:sanctum')->post('unsubscribe', 'unsubscribe')->name('mlk.api.subscription.unsubscribe');
        });
        
        // 获取订阅状态 - 添加较宽松的限流，每分钟最多10次请求
        Route::middleware('throttle:10,1;auth:sanctum')->get('status', 'status')->name('mlk.api.subscription.status');
    });

    /**
     * 购物车相关路由
     */
    Route::controller(CartController::class)->prefix('cart')->middleware('auth:sanctum')->group(function () {
        // 获取购物车内容
        Route::get('', 'index')->name('mlk.api.cart.index');

        // 添加商品到购物车
        Route::post('add', 'add')->name('mlk.api.cart.add');

        // 移除购物车商品
        Route::post('remove', 'remove')->name('mlk.api.cart.remove');

        // 更新购物车商品数量
        Route::post('update', 'update')->name('mlk.api.cart.update');

        // 清空购物车
        Route::post('clear', 'clear')->name('mlk.api.cart.clear');

        // 批量删除选中商品
        Route::post('remove-selected', 'destroySelected')->name('mlk.api.cart.remove_selected');

        // 移至愿望清单
        Route::post('move-to-wishlist', 'moveToWishlist')->name('mlk.api.cart.move_to_wishlist');

        // 应用优惠券
        Route::post('coupon/apply', 'applyCoupon')->name('mlk.api.cart.coupon.apply');

        // 移除优惠券
        Route::post('coupon/remove', 'removeCoupon')->name('mlk.api.cart.coupon.remove');

        // 估算运费
        Route::post('estimate-shipping', 'estimateShippingMethods')->name('mlk.api.cart.estimate_shipping');

        // 交叉销售商品
        Route::get('cross-sell', 'crossSellProducts')->name('mlk.api.cart.cross_sell');
    });

    /**
     * 结账相关路由
     */
    Route::controller(CheckoutController::class)->prefix('checkout')->middleware('auth:sanctum')->group(function () {
        // 结账摘要
        Route::get('summary', 'summary')->name('mlk.api.checkout.summary');

        // 保存地址
        Route::post('addresses', 'storeAddress')->name('mlk.api.checkout.addresses.store');

        // 保存配送方式
        Route::post('shipping-methods', 'storeShippingMethod')->name('mlk.api.checkout.shipping_methods.store');

        // 保存支付方式
        Route::post('payment-methods', 'storePaymentMethod')->name('mlk.api.checkout.payment_methods.store');

        // 提交订单
        Route::post('orders', 'storeOrder')->name('mlk.api.checkout.orders.store');
    });

    /**
     * 心愿单相关路由
     */
    Route::controller(WishlistController::class)->prefix('wishlist')->middleware('auth:sanctum')->group(function () {
        // 获取心愿单列表
        Route::get('', 'index')->name('mlk.api.wishlist.index');
        
        // 添加/删除商品到心愿单
        Route::post('', 'store')->name('mlk.api.wishlist.store');
        
        // 将心愿单商品移动到购物车
        Route::post('move-to-cart', 'moveToCart')->name('mlk.api.wishlist.move_to_cart');
        
        // 删除单个心愿单商品
        Route::post('delete', 'destroy')->name('mlk.api.wishlist.destroy');
        
        // 清空心愿单
        Route::post('clear', 'destroyAll')->name('mlk.api.wishlist.destroy_all');
    });

    /**
     * 客户相关路由
     */
    Route::controller(CustomerController::class)->prefix('customer')->group(function () {
        Route::post('login', 'login')->name('mlk.api.customers.login');
        
        Route::post('register', 'register')->name('mlk.api.customers.register');
        
        // 密码相关路由（无需认证）
        Route::post('forgot-password', 'forgotPassword')->name('mlk.api.customers.forgot_password');
        Route::post('reset-password', 'resetPassword')->name('mlk.api.customers.reset_password');
        
        // 邮箱验证相关路由（无需认证）
        Route::post('verify-account', 'verifyAccount')->name('mlk.api.customers.verify_account');
        Route::post('resend-verification', 'resendVerificationEmail')->name('mlk.api.customers.resend_verification');
        
        // 联系我们功能（无需认证）
        Route::get('contact-us/translations', 'getContactUsTranslations')->name('mlk.api.customers.contact_us.translations');
        Route::middleware('throttle:5,1')->post('contact-us', 'sendContactUs')->name('mlk.api.customers.contact_us');
        
        // 需要登录认证的路由 - 使用sanctum中间件
        Route::middleware('auth:sanctum')->group(function () {
           
            Route::post('refresh-token', 'refreshToken')->name('mlk.api.customers.refresh_token');
            
            Route::post('logout', 'logout')->name('mlk.api.customers.logout');
        });
    });

    /**
     * 用户中心dashboard相关路由
     */
    Route::controller(CustomerDashboardController::class)->prefix('dashboard')->middleware('auth:sanctum')->group(function () {
        // 获取用户信息
        Route::get('profile', 'profile')->name('mlk.api.dashboard.profile');
        
        // 更新用户头像
        Route::post('update-avatar', 'updateAvatar')->name('mlk.api.dashboard.update_avatar');
        
        // 删除用户头像
        Route::post('delete-avatar', 'deleteAvatar')->name('mlk.api.dashboard.delete_avatar');
        
        // 更新用户基本信息（昵称等）
        Route::post('update-profile', 'updateProfile')->name('mlk.api.dashboard.update_profile');
        
        // 获取用户订单列表
        Route::get('orders', 'orders')->name('mlk.api.dashboard.orders');
        
        // 获取订单详情
        Route::get('order-detail', 'orderDetail')->name('mlk.api.dashboard.order_detail');
    });

    /**
     * 地址管理相关路由
     */
    Route::controller(AddressController::class)->prefix('addresses')->middleware('auth:sanctum')->group(function () {
        // 获取地址列表
        Route::get('', 'index')->name('mlk.api.addresses.index');
        
        // 创建新地址
        Route::post('', 'store')->name('mlk.api.addresses.store');
        
        // 获取地址详情
        Route::get('detail', 'show')->name('mlk.api.addresses.show');
        
        // 更新地址
        Route::post('update', 'update')->name('mlk.api.addresses.update');
        
        // 设置默认地址
        Route::post('set-default', 'setDefault')->name('mlk.api.addresses.set_default');
        
        // 删除地址
        Route::delete('delete', 'destroy')->name('mlk.api.addresses.destroy');
    });

    /**
     * 优惠券相关路由
     */
    Route::controller(CustomerCouponController::class)->prefix('coupons')->middleware('auth:sanctum')->group(function () {
        // 获取优惠券列表
        Route::get('', 'index')->name('mlk.api.coupons.index');

    });

    /**
     * 订单管理相关路由
     */
    Route::controller(OrderController::class)->prefix('orders')->middleware('auth:sanctum')->group(function () {
        // 获取订单列表
        Route::get('', 'index')->name('mlk.api.orders.index');

        // 获取订单详情
        Route::get('{id}', 'show')->name('mlk.api.orders.show');

        // 重新下单
        Route::post('{id}/reorder', 'reorder')->name('mlk.api.orders.reorder');

        // 取消订单
        Route::post('{id}/cancel', 'cancel')->name('mlk.api.orders.cancel');

        // 下载发票
        Route::get('{id}/invoice', 'downloadInvoice')->name('mlk.api.orders.download_invoice');

        // 获取订单状态统计
        Route::get('stats/status', 'statusStats')->name('mlk.api.orders.status_stats');
    });

    /**
     * 发票管理相关路由
     */
    Route::controller(InvoiceController::class)->prefix('invoices')->middleware('auth:sanctum')->group(function () {
        // 获取发票列表
        Route::get('', 'index')->name('mlk.api.invoices.index');

        // 获取发票详情
        Route::get('{id}', 'show')->name('mlk.api.invoices.show');

        // 下载发票PDF
        Route::get('{id}/download', 'download')->name('mlk.api.invoices.download');

        // 获取发票状态统计
        Route::get('stats/status', 'statusStats')->name('mlk.api.invoices.status_stats');

        // 获取发票金额统计
        Route::get('stats/amount', 'amountStats')->name('mlk.api.invoices.amount_stats');
    });

    // 获取促销产品
    Route::get('promotions/special-products', [PromotionController::class, 'getSpecialProducts'])
        ->name('api.promotions.special-products');
    
    /**
     * 分类产品相关路由
     */
    Route::controller(CategoryProductController::class)->prefix('category')->group(function () {
        // 通过多个分类ID查询产品列表（包含子分类）
        Route::post('by-categories', 'getProductsByCategories')->name('mlk.api.category.products.by_categories');
        
        // 获取分类树及产品数量统计
        Route::post('category-tree-count', 'getCategoryTreeWithProductsCount')->name('mlk.api.category.products.tree_count');
    });


    
    /**
     * 新闻相关路由
     */
    Route::controller(NewsController::class)->prefix('news')->group(function () {
        // 获取新闻列表
        Route::get('', 'index')->name('mlk.api.news.index');
        
        // 获取标签列表
        Route::get('tags', 'getTags')->name('mlk.api.news.tags');
        
        // 获取文章详情 - 仅支持通过ID参数查询
        Route::get('detail', 'show')->name('mlk.api.news.show');
    });
});